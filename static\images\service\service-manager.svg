<svg width="40" height="47" viewBox="0 0 40 47" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_2283_1921)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M36.0002 26.9573C36.0002 23.4029 33.0901 20.5221 29.4994 20.5221H20.7195C17.1288 20.5221 14.2188 23.4029 14.2188 26.9573C14.2188 28.7341 15.6738 30.1744 17.4697 30.1744H32.7503C34.5451 30.1744 36.0002 28.7341 36.0002 26.9573Z" fill="url(#paint0_linear_2283_1921)"/>
</g>
<g filter="url(#filter1_i_2283_1921)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M25.1097 6.43488C28.7476 6.43488 31.6979 9.29602 31.6979 12.8263C31.6979 16.3555 28.7476 19.2177 25.1097 19.2177C21.4707 19.2177 18.5215 16.3555 18.5215 12.8263C18.5215 9.29602 21.4707 6.43488 25.1097 6.43488Z" fill="url(#paint1_linear_2283_1921)"/>
</g>
<foreignObject x="-4.65485" y="11.2914" width="45.0646" height="29.3634"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(4.08px);clip-path:url(#bgblur_0_2283_1921_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter2_i_2283_1921)" data-figma-bg-blur-radius="8.15485">
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.7552 27.9825C31.7552 23.5437 28.047 19.9463 23.4715 19.9463H12.2837C7.70822 19.9463 4 23.5437 4 27.9825C4 30.2013 5.85411 32 8.1425 32H27.614C29.9011 32 31.7552 30.2013 31.7552 27.9825Z" fill="#FFEFE6" style="mix-blend-mode:multiply"/>
<path d="M31.7552 27.9825V27.9825C31.7552 23.5437 28.047 19.9463 23.4715 19.9463H12.2837C7.70822 19.9463 4 23.5437 4 27.9825C4 30.2013 5.85411 32 8.1425 32H27.614C29.9011 32 31.7552 30.2013 31.7552 27.9825" stroke="white" stroke-opacity="0.27451"/>
</g>
<foreignObject x="0.992616" y="-6.65485" width="33.9816" height="33.4839"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(4.08px);clip-path:url(#bgblur_1_2283_1921_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter3_i_2283_1921)" data-figma-bg-blur-radius="8.15485">
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.9836 2C22.5866 2 26.3197 5.62023 26.3197 10.0871C26.3197 14.5527 22.5866 18.1742 17.9836 18.1742C13.3792 18.1742 9.64746 14.5527 9.64746 10.0871C9.64746 5.62023 13.3792 2 17.9836 2Z" fill="#FFEFE6" style="mix-blend-mode:multiply"/>
<path d="M17.9836 2C22.5866 2 26.3197 5.62023 26.3197 10.0871C26.3197 14.5527 22.5866 18.1742 17.9836 18.1742C13.3792 18.1742 9.64746 14.5527 9.64746 10.0871C9.64746 5.62023 13.3792 2 17.9836 2" stroke="white" stroke-opacity="0.27451"/>
</g>
<g filter="url(#filter4_d_2283_1921)">
<path d="M13.6973 12.3125C13.6973 12.3125 14.9709 14.1875 17.6339 14.1875C20.297 14.1875 21.4548 12.3125 21.4548 12.3125" stroke="white" stroke-linecap="round"/>
</g>
<defs>
<filter id="filter0_i_2283_1921" x="14.2188" y="20.5221" width="21.7812" height="10.6523" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.803922 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2283_1921"/>
</filter>
<filter id="filter1_i_2283_1921" x="18.5215" y="6.43488" width="13.1768" height="14.7828" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.803922 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2283_1921"/>
</filter>
<filter id="filter2_i_2283_1921" x="-4.65485" y="11.2914" width="45.0646" height="29.3634" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.886275 0 0 0 0 0.85098 0 0 0 0.301961 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2283_1921"/>
</filter>
<clipPath id="bgblur_0_2283_1921_clip_path" transform="translate(4.65485 -11.2914)"><path fill-rule="evenodd" clip-rule="evenodd" d="M31.7552 27.9825C31.7552 23.5437 28.047 19.9463 23.4715 19.9463H12.2837C7.70822 19.9463 4 23.5437 4 27.9825C4 30.2013 5.85411 32 8.1425 32H27.614C29.9011 32 31.7552 30.2013 31.7552 27.9825Z"/>
</clipPath><filter id="filter3_i_2283_1921" x="0.992616" y="-6.65485" width="33.9816" height="33.4839" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.886275 0 0 0 0 0.85098 0 0 0 0.301961 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2283_1921"/>
</filter>
<clipPath id="bgblur_1_2283_1921_clip_path" transform="translate(-0.992616 6.65485)"><path fill-rule="evenodd" clip-rule="evenodd" d="M17.9836 2C22.5866 2 26.3197 5.62023 26.3197 10.0871C26.3197 14.5527 22.5866 18.1742 17.9836 18.1742C13.3792 18.1742 9.64746 14.5527 9.64746 10.0871C9.64746 5.62023 13.3792 2 17.9836 2Z"/>
</clipPath><filter id="filter4_d_2283_1921" x="-10.8027" y="-4.18756" width="56.7578" height="50.8751" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="12"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.85098 0 0 0 0 0.431373 0 0 0 0 0.223529 0 0 0 0.396078 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2283_1921"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2283_1921" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2283_1921" x1="9.1986" y1="24.1881" x2="9.80134" y2="32.4418" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFBF62"/>
<stop offset="1" stop-color="#FF603E"/>
</linearGradient>
<linearGradient id="paint1_linear_2283_1921" x1="15.4846" y1="11.2898" x2="17.1976" y2="22.0049" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFBF62"/>
<stop offset="1" stop-color="#FF603E"/>
</linearGradient>
</defs>
</svg>
