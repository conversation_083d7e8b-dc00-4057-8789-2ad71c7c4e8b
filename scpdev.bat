@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

REM ========== 配置区域 ==========
set "SOURCE_DIR=H:\TIANXING\server\crmeb\uniapp\unpackage\dist\build\web"
set "TARGET_DIR=H:\TIANXING\server\crmeb\dev_sftp\h5.dev.hunantianxing.com"
REM =============================

echo.
echo 正在执行目录复制操作...
echo 源目录: %SOURCE_DIR%
echo 目标目录: %TARGET_DIR%
echo.

REM 检查源目录是否存在
if not exist "%SOURCE_DIR%" (
    echo 错误：源目录不存在！
    pause
    exit /b 1
)

REM 创建目标目录（如果不存在）
if not exist "%TARGET_DIR%" (
    echo 创建目标目录...
    mkdir "%TARGET_DIR%"
)

REM 执行复制操作（包含覆盖功能）
echo 开始复制文件（自动覆盖已有文件）...
xcopy "%SOURCE_DIR%\*" "%TARGET_DIR%\" /E /C /I /H /Y /K

if %errorlevel% equ 0 (
    echo.
    echo 操作成功完成！文件已复制到目标目录。
) else (
    echo.
    echo 复制过程中发生错误！错误代码: %errorlevel%
)

echo.
pause