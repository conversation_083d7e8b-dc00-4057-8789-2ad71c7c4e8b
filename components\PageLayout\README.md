# PageLayout 组件使用说明

## 组件概述

`PageLayout` 是一个通用的页面布局组件，封装了常见的页面结构，包括导航栏、背景遮罩和底部导航。

## 基本用法

### 1. 最简单的使用方式

```vue
<template>
  <PageLayout>
    <view class="content">
      页面内容
    </view>
  </PageLayout>
</template>

<script>
import PageLayout from "@/components/PageLayout";

export default {
  components: {
    PageLayout,
  },
};
</script>
```

### 2. 自定义导航栏属性

```vue
<template>
  <PageLayout
    iconColor="#fff"
    :isBackgroundColor="true"
    :isShowBack="true"
    navTitle="我的页面"
    backgroundColor="#007AFF"
    :isShowMenu="false"
  >
    <view class="content">
      页面内容
    </view>
  </PageLayout>
</template>
```

### 3. 自定义背景遮罩

```vue
<template>
  <PageLayout
    bgMaskClass="custom-bg"
    :bgMaskStyle="customBgStyle"
  >
    <view class="content">
      页面内容
    </view>
  </PageLayout>
</template>

<script>
export default {
  data() {
    return {
      customBgStyle: {
        background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4)',
        opacity: 0.8
      }
    };
  }
};
</script>

<style>
.custom-bg {
  border-radius: 20rpx;
}
</style>
```

### 4. 使用导航栏插槽

```vue
<template>
  <PageLayout navTitle="搜索页面">
    <template #nav-content>
      <input class="search-input" placeholder="搜索..." />
    </template>
    
    <view class="content">
      页面内容
    </view>
  </PageLayout>
</template>
```

### 5. 隐藏底部导航

```vue
<template>
  <PageLayout :showFooter="false">
    <view class="content">
      没有底部导航的页面内容
    </view>
  </PageLayout>
</template>
```

### 6. 使用自定义底部导航插槽

```vue
<template>
  <PageLayout>
    <view class="content">
      页面内容
    </view>

    <template #footer>
      <view class="custom-footer">
        <view class="footer-item">
          <text class="footer-icon">🏠</text>
          <text class="footer-text">首页</text>
        </view>
        <view class="footer-item">
          <text class="footer-icon">📂</text>
          <text class="footer-text">分类</text>
        </view>
        <view class="footer-item active">
          <text class="footer-icon">👤</text>
          <text class="footer-text">我的</text>
        </view>
      </view>
    </template>
  </PageLayout>
</template>

<style>
.custom-footer {
  display: flex;
  background: #fff;
  border-top: 1rpx solid #eee;
  padding-bottom: env(safe-area-inset-bottom);
}

.footer-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}

.footer-item.active {
  color: #007AFF;
}
</style>
```

## Props 说明

### 导航栏相关 Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| iconColor | String | "#000" | 图标和标题颜色 |
| isBackgroundColor | Boolean | false | 是否使用主题背景色 |
| isShowBack | Boolean | false | 是否显示返回按钮 |
| navTitle | String | "" | 导航栏标题 |
| backgroundColor | String | "transparent" | 导航栏背景色 |
| isShowMenu | Boolean | true | 是否显示菜单按钮 |
| isHeight | Boolean | true | 是否占用导航栏高度 |

### 背景遮罩相关 Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| bgMaskClass | String | "" | 自定义背景遮罩CSS类名 |
| bgMaskStyle | Object | {} | 自定义背景遮罩内联样式 |

### 底部导航相关 Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| showFooter | Boolean | true | 是否显示底部导航区域 |

## 插槽说明

| 插槽名 | 说明 |
|--------|------|
| default | 主要内容区域 |
| nav-content | 导航栏内容插槽 |
| footer | 底部导航插槽，默认使用 pageFooter 组件 |

## 注意事项

1. 组件使用了 `inheritAttrs: false`，所有未在 props 中定义的属性会通过 `v-bind="$attrs"` 传递给内部的 `nav-bar` 组件
2. 背景遮罩的默认样式会作为 fallback，自定义样式会覆盖默认样式
3. 条件编译逻辑（`#ifdef MP`）确保组件在小程序和其他平台都能正常工作
4. 导航高度会根据平台自动计算（小程序使用 globalData.navHeight，其他平台使用 96rpx）
5. 当 `showFooter` 为 false 时，底部导航区域将完全隐藏
6. 底部导航默认使用 pageFooter 组件，可以通过 footer 插槽自定义底部内容

## 底部导航功能

### 默认底部导航

组件默认使用 `pageFooter` 组件作为底部导航，会自动从后端获取导航配置并显示。

### showFooter 属性控制

通过 `showFooter` 属性可以控制是否显示底部导航区域：

- `showFooter="true"`（默认）：显示底部导航
- `showFooter="false"`：隐藏底部导航

```vue
<!-- 显示底部导航（默认行为） -->
<PageLayout>
  <view>内容</view>
</PageLayout>

<!-- 隐藏底部导航 -->
<PageLayout :showFooter="false">
  <view>内容</view>
</PageLayout>

<!-- 动态控制底部导航显示 -->
<PageLayout :showFooter="showFooterFlag">
  <view>内容</view>
  <button @click="showFooterFlag = !showFooterFlag">
    切换底部导航
  </button>
</PageLayout>
```

### 自定义底部导航

如果需要完全自定义底部导航，可以使用 `footer` 插槽：

```vue
<PageLayout>
  <view>页面内容</view>

  <template #footer>
    <view class="my-custom-footer">
      <!-- 自定义底部导航内容 -->
    </view>
  </template>
</PageLayout>
```

### 适用场景

- **显示默认导航**：大多数页面的标准场景
- **隐藏底部导航**：全屏展示页面、表单页面、详情页面
- **自定义底部导航**：需要特殊底部功能的页面
