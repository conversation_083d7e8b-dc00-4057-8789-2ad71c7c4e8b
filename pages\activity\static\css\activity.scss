	
	/**
	 * 拼团、积分商品列表页样式
	 */
	.w-210{
		width: 420rpx !important;
	}
	.activity-box{
		padding-bottom: calc(0rpx+ constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
		padding-bottom: calc(0rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
	}
	.activity_header {
		background-size: cover;
		position: relative;
		top: 0;
		left: 0;
		// z-index: 9;
		width: 100%;
		/* #ifdef H5 */
		height: 460rpx;
		/* #endif */
		/* #ifndef H5*/
		height: 540rpx;
		/* #endif */
	}
	.list-box{
		padding-bottom: calc(0rpx+ constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
		padding-bottom: calc(0rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
		position: relative;
		background-color: #f5f5f5;
		border-radius: 40rpx;
		padding: 20rpx 24rpx;
		// z-index: 99;
		/* #ifdef H5 */
		top: -40rpx;
		/* #endif */
		/* #ifndef H5*/
		top: -90rpx;
		/* #endif */
		height: 200rpx;
		.list-item{
			background-color: #fff;
			border-radius: 12px 12px 12px 12px;
			margin-bottom: 20rpx;
		}
	}
	.group-bottom {
	    width: 100%;
	    padding: 20rpx;
		
	    .img {
	        width: 240rpx;
	        height: 240rpx;
			image{
				width: 100%;
				height: 100%;
				border-radius: 20rpx;
			}
	    }
		
	    .big-img.img {
	        width: 100%;
	        height: 324rpx;
	    }
		
	    .three-img.img {
	        width: 100%;
	        height: 210rpx;
	    }
		
	    .four-img {
	        width: 240rpx;
	        height: 240rpx;
	    }
		
	    .group-bottom-right {
	        width: 400rpx;
		
	        .right-top {
	            .title {
	                font-size: 28rpx;
	                color: #333333;
	            }
		
	            .pink {
	                margin-top: 16rpx;
	                font-size: 22rpx;
		
	                .people {
						background-color: #E93323;
	                    color: #fff;
	                    padding: 4rpx 12rpx;
	                    border-radius: 8rpx 0 0 8rpx;
	                }
		
	                .groupNum {
	                    color: #E93323;
	                    background: rgba(211, 56, 42, 0.1);
	                    padding: 4rpx 12rpx;
	                    border-radius: 0 8rpx 8rpx 0;
	                }
	            }
	        }
		
	        .right-bottom {
		
	            .price {
		
	                .pinkNum {
		
	                    .pinkNum-num {
	                        font-weight: 600;
	                        font-size: 36rpx;
	                    }
	                }
		
	                .num {
	                    color: #999999;
						font-weight: 400;
						text-decoration-line: line-through;
	                }
	            }
		
	            .btnBox {
	                margin-top: 28rpx;
	                font-size: 22rpx;
		
	                .btn {
	                    padding: 12rpx 20rpx;
	                    border-radius: 50rpx;
						background: linear-gradient( 90deg, #FF7931 0%, #E93323 100%);
						border-radius: 25px 25px 25px 25px;
						color: #fff;
						font-size: 24rpx;
	                }
	            }
				.pinkNum-title{
					color: #E93323;
					font-size: 22rpx;
				}
	        }
	    }
	}