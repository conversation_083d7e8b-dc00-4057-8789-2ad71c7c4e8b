<svg width="40" height="45" viewBox="0 0 40 45" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_2283_1933)">
<path d="M25.1094 7.43488C28.7473 7.43488 31.6973 10.2962 31.6973 13.8265C31.6972 16.3118 30.2337 18.4647 28.0977 19.5218H29.499C33.0897 19.5218 36 22.4029 36 25.9573C36 27.734 34.5448 29.1741 32.75 29.1741H17.4697C15.6739 29.1741 14.2188 27.734 14.2188 25.9573C14.2188 22.4029 17.1291 19.5218 20.7197 19.5218H22.1211C19.9849 18.4648 18.5216 16.3119 18.5215 13.8265C18.5215 10.2962 21.4704 7.43492 25.1094 7.43488Z" fill="url(#paint0_linear_2283_1933)"/>
</g>
<g filter="url(#filter1_i_2283_1933)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M30.3194 9.0871C30.3194 4.62023 26.5863 1 21.9833 1C17.3789 1 13.6472 4.62023 13.6472 9.0871C13.6472 11.9789 15.212 14.5167 17.5654 15.9463H16.2837C11.7082 15.9463 8 19.5438 8 23.9825C8 26.2013 9.85411 28 12.1425 28H31.614C33.9011 28 35.7552 26.2013 35.7552 23.9825C35.7552 19.5438 32.047 15.9463 27.4715 15.9463H26.4005C28.7539 14.5167 30.3194 11.9789 30.3194 9.0871Z" fill="url(#paint1_linear_2283_1933)"/>
</g>
<foreignObject x="-2.89996" y="-7.65485" width="44.3097" height="48.3097"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(4.08px);clip-path:url(#bgblur_0_2283_1933_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter2_i_2283_1933)" data-figma-bg-blur-radius="8.15485">
<path fill-rule="evenodd" clip-rule="evenodd" d="M28.6719 9.0871C28.6719 4.62023 24.9389 1 20.3358 1C15.7314 1 11.9997 4.62023 11.9997 9.0871C11.9997 12.0154 13.6044 14.5808 16.0076 16H13.8132C9.36221 16 5.75488 20.2636 5.75488 25.5244C5.75488 28.154 7.55855 32 9.78468 32H28.7264C30.9512 32 32.7549 28.154 32.7549 25.5244C32.7549 20.2636 29.1476 16 24.6966 16H24.6634C27.0666 14.5808 28.6719 12.0154 28.6719 9.0871Z" fill="#FFEFE6" style="mix-blend-mode:multiply"/>
<path d="M16.0076 16V16.5H17.8375L16.2618 15.5695L16.0076 16ZM24.6634 16L24.4092 15.5695L22.8335 16.5H24.6634V16ZM20.3358 1V1.5C24.6772 1.5 28.1719 4.91061 28.1719 9.0871H28.6719H29.1719C29.1719 4.32985 25.2005 0.5 20.3358 0.5V1ZM11.9997 9.0871H12.4997C12.4997 4.91055 15.9931 1.5 20.3358 1.5V1V0.5C15.4697 0.5 11.4997 4.3299 11.4997 9.0871H11.9997ZM16.0076 16L16.2618 15.5695C14.0003 14.2339 12.4997 11.8265 12.4997 9.0871H11.9997H11.4997C11.4997 12.2044 13.2085 14.9277 15.7533 16.4305L16.0076 16ZM16.0076 16V15.5H13.8132V16V16.5H16.0076V16ZM13.8132 16V15.5C9.01098 15.5 5.25488 20.0691 5.25488 25.5244H5.75488H6.25488C6.25488 20.4582 9.71343 16.5 13.8132 16.5V16ZM5.75488 25.5244H5.25488C5.25488 26.9355 5.73333 28.6383 6.49969 29.9943C6.88434 30.6749 7.35419 31.2909 7.89584 31.7421C8.4383 32.194 9.0778 32.5 9.78468 32.5V32V31.5C9.37849 31.5 8.95772 31.3252 8.5359 30.9738C8.11327 30.6217 7.71481 30.1119 7.37027 29.5023C6.67827 28.2779 6.25488 26.743 6.25488 25.5244H5.75488ZM9.78468 32V32.5H28.7264V32V31.5H9.78468V32ZM28.7264 32V32.5C29.433 32.5 30.0723 32.194 30.6145 31.7421C31.156 31.2909 31.6257 30.6749 32.0103 29.9943C32.7764 28.6383 33.2549 26.9355 33.2549 25.5244H32.7549H32.2549C32.2549 26.743 31.8315 28.2779 31.1396 29.5023C30.7952 30.112 30.3968 30.6218 29.9743 30.9739C29.5527 31.3253 29.1322 31.5 28.7264 31.5V32ZM32.7549 25.5244H33.2549C33.2549 20.0691 29.4988 15.5 24.6966 15.5V16V16.5C28.7963 16.5 32.2549 20.4582 32.2549 25.5244H32.7549ZM24.6966 16V15.5H24.6634V16V16.5H24.6966V16ZM28.6719 9.0871H28.1719C28.1719 11.8264 26.6708 14.2338 24.4092 15.5695L24.6634 16L24.9177 16.4305C27.4624 14.9277 29.1719 12.2044 29.1719 9.0871H28.6719Z" fill="white" fill-opacity="0.27451"/>
</g>
<g filter="url(#filter3_d_2283_1933)">
<path d="M16.6963 10.3125C16.6963 10.3125 17.9699 12.1875 20.633 12.1875C23.296 12.1875 24.4539 10.3125 24.4539 10.3125" stroke="white" stroke-linecap="round"/>
</g>
<defs>
<filter id="filter0_i_2283_1933" x="14.2188" y="7.43488" width="21.7812" height="22.7393" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.803922 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2283_1933"/>
</filter>
<filter id="filter1_i_2283_1933" x="8" y="1" width="27.7549" height="28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.803922 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2283_1933"/>
</filter>
<filter id="filter2_i_2283_1933" x="-2.89996" y="-7.65485" width="44.3097" height="48.3097" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.886275 0 0 0 0 0.85098 0 0 0 0.301961 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2283_1933"/>
</filter>
<clipPath id="bgblur_0_2283_1933_clip_path" transform="translate(2.89996 7.65485)"><path fill-rule="evenodd" clip-rule="evenodd" d="M28.6719 9.0871C28.6719 4.62023 24.9389 1 20.3358 1C15.7314 1 11.9997 4.62023 11.9997 9.0871C11.9997 12.0154 13.6044 14.5808 16.0076 16H13.8132C9.36221 16 5.75488 20.2636 5.75488 25.5244C5.75488 28.154 7.55855 32 9.78468 32H28.7264C30.9512 32 32.7549 28.154 32.7549 25.5244C32.7549 20.2636 29.1476 16 24.6966 16H24.6634C27.0666 14.5808 28.6719 12.0154 28.6719 9.0871Z"/>
</clipPath><filter id="filter3_d_2283_1933" x="-7.80371" y="-6.18756" width="56.7578" height="50.8751" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="12"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.85098 0 0 0 0 0.431373 0 0 0 0 0.223529 0 0 0 0.396078 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2283_1933"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2283_1933" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2283_1933" x1="9.19864" y1="15.6914" x2="12.1914" y2="33.8877" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFBF62"/>
<stop offset="1" stop-color="#FF603E"/>
</linearGradient>
<linearGradient id="paint1_linear_2283_1933" x1="1.60302" y1="11.2546" x2="5.23067" y2="33.8841" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFBF62"/>
<stop offset="1" stop-color="#FF603E"/>
</linearGradient>
</defs>
</svg>
