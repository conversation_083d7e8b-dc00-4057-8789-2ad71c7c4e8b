{
  "compilerOptions": {
    // 编译目标版本，支持ES6语法
    "target": "es2015",
    // 模块系统
    "module": "esnext",
    // 模块解析策略
    "moduleResolution": "node",
    // 基础URL，用于解析非相对模块名
    "baseUrl": ".",
    // 路径映射配置
    "paths": {
      // @/ 别名指向项目根目录
      "@/*": ["./*"],
      // 支持直接导入utils目录
      "utils/*": ["./utils/*"],
      // 支持直接导入components目录
      "components/*": ["./components/*"],
      // 支持直接导入api目录
      "api/*": ["./api/*"],
      // 支持直接导入libs目录
      "libs/*": ["./libs/*"],
      // 支持直接导入config目录
      "config/*": ["./config/*"],
      // 支持直接导入store目录
      "store/*": ["./store/*"],
      // 支持直接导入mixins目录
      "mixins/*": ["./mixins/*"],
      // 支持直接导入filters目录
      "filters/*": ["./filters/*"],
      // 支持直接导入static目录
      "static/*": ["./static/*"]
    },
    // 允许从没有默认导出的模块中默认导入
    "allowSyntheticDefaultImports": true,
    // 启用ES模块互操作性
    "esModuleInterop": true,
    // 支持装饰器语法
    "experimentalDecorators": true,
    // JSX处理方式
    "jsx": "preserve",
    // 包含的库文件
    "lib": [
      "es2015",
      "es2017",
      "dom",
      "dom.iterable"
    ],
    // 不检查JS文件的类型（提高性能）
    "checkJs": false,
    // 允许导入JSON文件
    "resolveJsonModule": true,
    // 严格模式相关配置（适度宽松以适应UniApp）
    "strict": false,
    // 不报告未使用的局部变量
    "noUnusedLocals": false,
    // 不报告未使用的参数
    "noUnusedParameters": false,
    // 允许隐式any类型
    "noImplicitAny": false,
    // 跳过库文件的类型检查
    "skipLibCheck": true
  },
  // 包含的文件和目录
  "include": [
    // 包含所有JavaScript文件
    "**/*.js",
    // 包含所有Vue单文件组件
    "**/*.vue",
    // 包含所有JSON配置文件
    "**/*.json",
    // 包含UniApp原生渲染文件
    "**/*.nvue",
    // 包含微信小程序脚本文件
    "**/*.wxs",
    // 包含支付宝小程序脚本文件
    "**/*.sjs",
    // 包含TypeScript声明文件（如果有的话）
    "**/*.d.ts"
  ],
  // 排除的文件和目录
  "exclude": [
    // 排除node_modules依赖包
    "node_modules",
    // 排除打包输出目录
    "unpackage",
    // 排除Android打包工作目录
    "AndroidPackWork",
    // 排除临时文件
    "**/*.tmp",
    // 排除日志文件
    "**/*.log",
    // 排除批处理文件
    "**/*.bat"
  ],
  // VS Code特定配置
  "vueCompilerOptions": {
    // 支持Vue 2.x语法
    "target": 2.7,
    // 手动设置全局类型文件路径
    "globalTypesPath": "./types/global.d.ts"
  }
}
