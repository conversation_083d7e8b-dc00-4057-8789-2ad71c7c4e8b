<template>
  <view class="page-c">
    <!-- #ifdef MP -->
    <!-- 顶部导航 -->
    <view class="nav-container" :style="'height:' + navH + 'rpx;'">
      <nav-bar
        :iconColor="iconColor"
        :isBackgroundColor="isBackgroundColor"
        :isShowBack="isShowBack"
        :navTitle="navTitle"
        :backgroundColor="backgroundColor"
        :isShowMenu="isShowMenu"
        :isHeight="isHeight"
      >
        <template #default>
          <slot name="nav-content"></slot>
        </template>
      </nav-bar>
    </view>
    <!-- #endif -->

    <!-- 背景遮罩 -->
    <view
      :class="['bg-mask', bgMaskClass]"
      :style="[bgMaskStyle]"
    ></view>

    <!-- 内容区域 -->
    <view class="content-c" :class="{ 'has-footer': showFooter }">
      <slot></slot>
    </view>

    <!-- 底部导航 -->
    <pageFooter v-if="showFooter"></pageFooter>
  </view>
</template>

<script>
import navBar from "@/components/navBar.vue";
import pageFooter from "@/components/pageFooter/index.vue";
import { mapGetters } from "vuex";

const app = getApp();

export default {
  name: "PageLayout",
  inheritAttrs: false, // 禁用自动继承属性，手动控制传递给nav-bar

  data() {
    return {
      navH: "",
    };
  },
  mounted() {
    console.log("PageLayout mounted, style:", this.$attrs);
  },

  props: {
    /**
     * 导航栏图标和标题颜色
     * @type {String}
     * @default "#000"
     */
    iconColor: {
      type: String,
      default: "#000",
    },

    /**
     * 是否使用主题背景色
     * @type {Boolean}
     * @default false
     */
    isBackgroundColor: {
      type: Boolean,
      default: false,
    },

    /**
     * 是否显示返回按钮
     * @type {Boolean}
     * @default false
     */
    isShowBack: {
      type: Boolean,
      default: false,
    },

    /**
     * 导航栏标题
     * @type {String}
     * @default ""
     */
    navTitle: {
      type: String,
      default: "",
    },

    /**
     * 导航栏背景色
     * @type {String}
     * @default "transparent"
     */
    backgroundColor: {
      type: String,
      default: "transparent",
    },

    /**
     * 是否显示菜单按钮
     * @type {Boolean}
     * @default true
     */
    isShowMenu: {
      type: Boolean,
      default: true,
    },

    /**
     * 是否占用导航栏高度
     * @type {Boolean}
     * @default true
     */
    isHeight: {
      type: Boolean,
      default: true,
    },

    /**
     * 自定义背景遮罩CSS类名
     * @type {String}
     * @default ""
     */
    bgMaskClass: {
      type: String,
      default: "",
    },

    /**
     * 自定义背景遮罩内联样式对象
     * @type {Object}
     * @default {}
     */
    bgMaskStyle: {
      type: Object,
      default: () => ({}),
    },

    /**
     * 是否显示底部导航区域
     * @type {Boolean}
     * @default true
     */
    showFooter: {
      type: Boolean,
      default: true,
    },
  },

  computed: {
    ...mapGetters(["globalData"]),
  },

  components: {
    pageFooter,
    navBar,
  },

  /**
   * 页面加载时初始化导航高度
   */
  onLoad() {
    // #ifdef MP
    this.navH = this.globalData.navHeight;
    // #endif
    // #ifndef MP
    this.navH = 96;
    // #endif
  },
};
</script>

<style lang="scss" scoped>
.page-c {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.nav-container {
  flex-shrink: 0; /* 防止导航栏被压缩 */
  z-index: 100;
}

.bg-mask {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;

  /* 支持CSS变量自定义背景，提供默认的渐变背景 */
  background: var(--bg-mask-background, transparent);
}

.content-c {
  position: relative;
  flex: 1;
  z-index: 1;
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden; /* 防止水平滚动 */

  /* 确保容器高度固定，不随内容变化 */
  min-height: 0; /* 重要：允许 flex 子项收缩 */

  /* 为底部导航预留空间 */
  &.has-footer {
    padding-bottom: calc(98rpx + env(safe-area-inset-bottom));
    padding-bottom: calc(
      98rpx + constant(safe-area-inset-bottom)
    ); /* iOS < 11.2 兼容 */
  }
}
</style>
