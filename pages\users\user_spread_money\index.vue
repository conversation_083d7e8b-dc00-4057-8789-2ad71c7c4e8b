<template>
	<view :data-theme="theme">
		<view class='commission-details'>
			<view class='promoterHeader'>
				<view class='headerCon acea-row row-between-wrapper'>
					<view>
						<view class='name'>{{name}}</view>
						<view class='money' v-if="recordType == 4">￥<text class='num'>{{extractCount}}</text></view>
						<view class='money' v-else>￥<text class='num semiBold'>{{commissionCount}}</text></view>
					</view>
          <view class="commission-pic"><image src="../static/images/commission.png" class="w-100-p111- h-100-p111-"/></view>
				</view>
			</view>
			<view class='sign-record' v-if="recordType == 4">
				<block v-for="(item,index) in recordList" :key="index" v-if="recordList.length>0">
					<view class='list borderPad'>
						<view class='item'>
							<view class='data'>{{item.month}}</view>
							<view class='listn borRadius14'>
								<block v-for="(child,indexn) in item.list" :key="indexn">
									<view class='itemn acea-row row-between-wrapper'>
										<view style="width: 70%;">
											<view v-if="child.auditStatus!==1" class='name line1'>
												{{child.auditStatus | statusFilter}}
											</view>
											<view class='name line1' v-else>审核成功 / {{child.accountStatus==0?'待结算':'已结算'}}
											</view>
											<view v-show="child.auditStatus==2 && child.refusalReason" class="font-color-red mb8">{{child.refusalReason}}</view>
											<view>{{child.createTime}}</view>
										</view>
										<view class='num font_color'>{{child.closingPrice}}</view>
									</view>
								</block>
							</view>
						</view>
					</view>
				</block>
				<view v-if="recordList.length == 0">
					<emptyPage title='暂无结算记录~' :imgSrc="urlDomain+'crmebimage/presets/noJilu.png'"></emptyPage>
				</view>
			</view>
			<view class='sign-record' v-else>
				<block v-for="(item,index) in recordList" :key="index" v-if="recordList.length>0">
					<view class='list borderPad'>
						<view class='item'>
							<view class='data'>{{item.month}}</view>
							<view class='listn borRadius14'>
								<block v-for="(child,indexn) in item.list" :key="indexn">
									<view class='itemn acea-row row-between-wrapper'>
										<view>
											<view class='name line1'>{{child.title}}</view>
											<view>{{child.updateTime}}</view>
										</view>
										<view class='num font_color' v-if="child.type == 1">+{{child.price}}
										</view>
										<view class='num' v-else>-{{child.price}}</view>
									</view>
								</block>
							</view>
						</view>
					</view>
				</block>
				<view v-if="recordList.length == 0">
					<emptyPage title='暂无佣金记录~' :imgSrc="urlDomain+'crmebimage/presets/noJilu.png'"></emptyPage>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		getCommissionInfo,
		getClosingRecordApi,
	} from '@/api/user.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import emptyPage from '@/components/emptyPage.vue'
	import {
		setThemeColor
	} from '@/utils/setTheme.js'
	const app = getApp();
	export default {
		components: {
			emptyPage
		},
		filters: {
			statusFilter(status) {
				const statusMap = {
					'2': '已拒绝',
					'0': '待审核',
					'1': '审核成功'
				}
				return statusMap[status]
			}
		},
		data() {
			return {
				urlDomain: this.$Cache.get("imgHost"),
				name: '',
				type: 0,
				page: 1,
				limit: 20,
				recordList: [],
				recordType: 0,
				loadend: false,
				extractCount: 0,
				theme: app.globalData.theme,
				commissionCount: 0,
				bgColor: '#e93323'
			};
		},
		computed: mapGetters(['isLogin']),
		onLoad(options) {
      this.type = options.type;
			if (this.isLogin) {
				this.extractCount = options.extractCount;
				this.commissionCount = options.commissionCount;
			} else {
				toLogin();
			}
			let that = this;
			that.bgColor = setThemeColor();
			uni.setNavigationBarColor({
				frontColor: '#ffffff',
				backgroundColor: that.bgColor,
			});
		},
		onShow: function() {
			let type = this.type;
			if (type == 1) {
				uni.setNavigationBarTitle({
					title: "结算记录"
				});
				this.name = '结算总额';
				this.recordType = 4;
				this.getList();
			} else if (type == 2) {
				uni.setNavigationBarTitle({
					title: "佣金记录"
				});
				this.name = '佣金记录';
				this.recordType = 3;
				this.getRecordList();
			} else {
				uni.showToast({
					title: '参数错误',
					icon: 'none',
					duration: 1000,
					mask: true,
					success: function(res) {
						setTimeout(function() {
							// #ifndef H5
							uni.navigateBack({
								delta: 1,
							});
							// #endif
							// #ifdef H5
							history.back();
							// #endif

						}, 1200)
					},
				});
			}
		},
		methods: {
			getList: function() {
				let that = this;
				let recordList = that.recordList;
				let recordListNew = [];
				if (that.loadend == true) return;
				getClosingRecordApi({
					page: that.page,
					limit: that.limit
				}).then(res => {
					that.page = that.page + 1;
					let len = that.page > res.data.totalPage;
					let recordListData = res.data || [];
					recordListNew = recordList.concat(recordListData);
					that.loadend = len;
					that.$set(that, 'recordList', recordListNew);
				});
			},
			getRecordList: function() {
				let that = this;
				let page = that.page;
				let limit = that.limit;
				let loadend = that.loadend;
				let recordType = that.recordType;
				let recordList = that.recordList;
				let recordListNew = [];
				if (loadend == true) return;
				getCommissionInfo({
					page: page,
					limit: limit
				}).then(res => {
					if (res.data.list) {
						that.page = page + 1;
						let len = that.page > res.data.totalPage;
						let recordListData = res.data.list || [];
						recordListNew = recordList.concat(recordListData);
						that.loadend = len;
						that.$set(that, 'recordList', recordListNew);
					}
				});
			}
		},
		onReachBottom: function() {
			if (this.type == 1) {
				this.getList();
			} else {
				this.getRecordList();
			}

		}
	}
</script>

<style scoped lang="scss">
.commission-pic{
  width: 116rpx;
  height: 116rpx;
}
	.commission-details .promoterHeader .headerCon .money {
		font-size: 36rpx;
	}

	.promoterHeader {
		@include main_bg_color(theme);
	}

	.font_color {
		color: #E93323 !important;
	}
</style>