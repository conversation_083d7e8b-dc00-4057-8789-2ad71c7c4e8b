<template>
	<view :data-theme="theme" class="bg--w111-fff">
		<view class='searchGood bg--w111-fff'>
			<view class='search acea-row row-between-wrapper'>
				<!-- #ifndef MP -->
				<view class='input acea-row row-between-wrapper'>
					<text class='iconfont icon-ic_search'></text>
					<input type='text' :value='searchValue'
					:focus="focus" placeholder='点击搜索商品'
					confirm-type='search' @confirm="searchBut"
					placeholder-class='placeholder' @input="setValue"
					maxlength="20"></input>
				</view>
				<view class='bnt' @tap='searchBut'>搜索</view>
				<!-- #endif -->
				<!-- #ifdef MP -->
				<searchBox :searchValue="searchValue" class="searchBox"  @searchChange="searchBut"></searchBox>
				<!-- #endif -->
			</view>
			<!-- #ifdef MP -->
			<view class='title' :style="{'padding-top':searchTop+60+'px'}">热门搜索</view>
			<!-- #endif -->
			<!-- #ifndef MP -->
			<view class='title'>热门搜索</view>
			<!-- #endif -->
			<view class='list acea-row'>
				<block v-for="(item,index) in hotSearchList" :key="index">
					<view class='item' @tap='setHotSearchValue(item.title)'>{{item.title}}</view>
				</block>
			</view>
			<view class='line'></view>
		</view>
		<recommend ref="recommend"></recommend>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		getSearchKeyword,
	} from '@/api/product.js';
	import recommend from "@/components/base/recommend.vue";
	// #ifdef MP
	import searchBox from "@/components/searchBox.vue";
	// #endif
	let app = getApp();
	export default {
		components: {
			recommend,
			// #ifdef MP
			searchBox
			// #endif
		},
		data() {
			return {
				hostProduct: [],
				searchValue: '',
				focus: true,
				bastList: [],
				hotSearchList: [],
				first: 0,
				limit: 8,
				page: 1,
				loading: false,
				loadend: false,
				loadTitle: '加载更多',
				hotPage:1,
				isScroll:true,
				theme:app.globalData.theme,
				searchTop:''
			};
		},
		// 滚动监听
		onPageScroll(e) {
			// 传入scrollTop值并触发所有easy-loadimage组件下的滚动监听事件
			uni.$emit('scroll');
		},
		onShow: function() {
			this.getRoutineHotSearch();
		},
		onLoad(e){
			// #ifdef MP
			this.searchTop=uni.getMenuButtonBoundingClientRect().top
			// #endif
			if(e.searchVal) this.searchValue = e.searchVal;
		},
		methods: {
			getRoutineHotSearch: function() {
				let that = this;
				getSearchKeyword().then(res => {
					that.$set(that, 'hotSearchList', res.data);
				});
			},
			setHotSearchValue: function(event) {
				this.$set(this, 'searchValue', event);
				this.page = 1;
				this.loadend = false;
				this.$set(this, 'bastList', []);
				uni.navigateTo({
					url: `/pages/goods/goods_list/index?searchValue=${this.searchValue}`
				})
			},
			setValue: function(event) {
				this.$set(this, 'searchValue', event.detail.value);
			},
			searchBut: function(e) {
				let that = this;
				let val = '';
				that.focus = false;
				if(e.detail.value){
					val =e.detail.value
				}
				if (that.searchValue.length > 0||val) {
					uni.navigateTo({
						url: `/pages/goods/goods_list/index?searchValue=${val?val:that.searchValue}`
					})
				} else {
					return this.$util.Tips({
						title: '请输入要搜索的商品',
						icon: 'none',
						duration: 1000,
						mask: true,
					});
				}
			}
		},
		onReachBottom() {
			this.$refs.recommend.get_host_product();
		}
	}
</script>

<style lang="scss" scoped>
    page,/deep/.recommendList {
		background-color: #fff !important;
	}
	.searchGood .search {
		padding-left: 30rpx;
		background-color: #fff !important;
	}

	.searchGood .search {
		/* #ifndef MP */
		padding-top: 20rpx;
		padding-bottom: 20rpx;
		/* #endif */
		/* #ifdef MP */
		position: fixed;
		width: 100%;
		z-index: 9999;
		top: 0;
		/* #endif */
	}

	.searchGood .search .input {
		width: 598rpx;
		background-color: #f7f7f7;
		border-radius: 33rpx;
		padding: 0 30rpx;
		box-sizing: border-box;
		height: 66rpx;
	}

	.searchGood .search .input input {
		width: 492rpx;
		font-size: 24rpx;
	}

	.searchGood .search .input .placeholder {
		color: #bbb;
	}

	.searchGood .search .bnt {
		width: 120rpx;
		text-align: center;
		height: 66rpx;
		line-height: 66rpx;
		font-size: 30rpx;
		color: #282828;
	}

	.searchGood .title {
		font-weight: 500;
		font-family: PingFang SC, PingFang SC;
		font-size: 28rpx;
		color: #333;
		margin: 0 30rpx 25rpx 30rpx;
		background-color: #fff;
	}

	.searchGood .list {
		padding-left: 10rpx;
	}

	.searchGood .list .item {
		font-size: 24rpx;
		color: #454545;
		padding: 0 24rpx;
		height: 60rpx;
		border-radius: 200rpx;
		line-height: 59rpx;
		background: #F5F5F5;
		margin: 0 0 16rpx 24rpx;
	}

	.searchGood .line {
		margin: 20rpx 30rpx 0 30rpx;
	}
	.searchBox{
		width: 100%;
	}
</style>
