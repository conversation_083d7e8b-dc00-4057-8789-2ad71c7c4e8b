<template>
	<view :data-theme="theme">
		<form @submit="formSubmit" report-submit='true'>
			<view class='evaluate-con borderPad'>
				<view class='score borRadius14'>
					<view class='item acea-row row-middle' v-for="(item,indexw) in scoreList" :key="indexw">
						<view>{{item.name}}</view>
						<view class='starsList'>
							<text @click="stars(indexn, indexw)" v-for="(itemn, indexn) in item.stars" :key="indexn"
								class='iconfont' :class="item.index >= indexn? 'icon-ic_star1':'icon-ic_star'"></text>
						</view>
						<text class='evaluate'>{{item.index === -1 ? "" : item.index + 1 + "分"}}</text>
					</view>
					<view class='textarea'>
						<textarea placeholder='商品满足你的期待么？说说你的想法，分享给想买的他们吧~' name="comment"
							placeholder-class='placeholder'></textarea>
						<view class='list acea-row row-middle'>
							<view class='pictrue' v-for="(item,index) in picsPath" :key="index">
								<image :src='item'></image>
								<view class="bottom_close" @click="DelPic(index)">
									<view class="iconfont icon-ic_close f-s-24"></view>
								</view>
								<!-- <text class='iconfont icon-ic_close2 font-color' @click='DelPic(index)'></text> -->
							</view>
							<view class='pictrue acea-row row-center-wrapper row-column' @click='uploadpic'
								v-if="picsPath.length < 8">
								<text class='f-s-50 text--w111-999 iconfont icon-ic_camera'></text>
								<view class="text--w111-999 line-heightOne mt-8rpx">上传图片</view>
							</view>
						</view>
					</view>
					<button class='evaluateBnt bg-color' formType="submit">立即评价</button>
				</view>
			</view>
		</form>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		orderComment
	} from '@/api/order.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import {
		Debounce
	} from '@/utils/validate.js'
	let app = getApp();
	export default {
		data() {
			return {
				pics: [],
				picsPath: [],
				scoreList: [{
						name: "商品质量",
						stars: ["", "", "", "", ""],
						index: -1
					},
					{
						name: "服务态度",
						stars: ["", "", "", "", ""],
						index: -1
					}
				],
				orderNo: '', //订单编号
				evaluateId: 0, //订单id
				unique: '',
				cart_num: 0,
				theme: app.globalData.theme,
			};
		},
		computed: mapGetters(['isLogin']),
		onLoad(options) {
			if (!options.orderNo) return this.$util.Tips({
				title: '缺少参数'
			}, {
				tab: 3,
				url: 1
			});
			this.orderNo = options.orderNo || 0;
			this.evaluateId = Number(options.id) || 0;
			if (!this.isLogin) {
				toLogin();
			}
		},
		methods: {
			stars: function(indexn, indexw) {
				this.scoreList[indexw].index = indexn;
			},
			/**
			 * 删除图片
			 *
			 */
			DelPic: function(index) {
				let that = this,
					pic = this.picsPath[index];
				that.picsPath.splice(index, 1);
				that.pics.splice(index, 1);
			},

			/**
			 * 上传文件
			 *
			 */
			uploadpic: function() {
				let that = this;
				that.$util.uploadImageOne({
					url: 'upload/image',
					name: 'multipart',
					model: "product",
					pid: 1
				}, function(res) {
					that.pics.push(res);
					that.picsPath.push(res);
					that.$set(that, 'pics', that.pics);
					that.$set(that, 'picsPath', that.picsPath);
				});
			},

			/**
			 * 立即评价
			 */
			formSubmit: Debounce(function(e) {
				let formId = e.detail.formId,
					value = e.detail.value,
					that = this,
					product_score = that.scoreList[0].index + 1 === 0 ? "" : that.scoreList[0].index + 1;
				// if (!value.comment) return that.$util.Tips({
				// 	title: this.$t(`page.users.goodsCommentCon.tipsWrite`)
				// });
				value.star = product_score;
				value.pics = that.pics;
				value.orderDetailId = that.evaluateId;
				value.orderNo = that.orderNo;
				uni.showLoading({
					title: "正在发布评论……"
				});
				orderComment(value).then(res => {
					uni.hideLoading();
					return that.$util.Tips({
						title: '感谢您的评价!',
						icon: 'success'
					}, '/pages/goods/evaluation_list/index');
				}).catch(err => {
					uni.hideLoading();
					return that.$util.Tips({
						title: err
					});
				});
			})
		}
	}
</script>

<style lang="scss" scoped>
	.goodsStyle .text .name,
	.attr {
		//width: 496rpx;
	}

	.font_sm {
		font-size: 24rpx;
		color: #999;
		padding-top: 10rpx;
	}

	.icon-ic_star1 {
		@include main_color(theme);
	}

	.evaluate-con .score {
		background-color: #fff;
		// border-top: 1rpx solid #f5f5f5;
		margin-top: 20rpx;
		font-size: 28rpx;
		color: #282828;
		padding: 46rpx 24rpx;
	}

	.evaluate-con .score .item~.item {
		margin-top: 36rpx;
	}

	.evaluate-con .score .item .starsList {
		padding: 0 35rpx 0 40rpx;
	}

	.evaluate-con .score .item .starsList .iconfont {
		font-size: 40rpx;
		color: #aaa;
	}

	.evaluate-con .score .item .starsList .iconfont~.iconfont {
		margin-left: 20rpx;
	}

	.evaluate-con .score .item .evaluate {
		color: #aaa;
		font-size: 24rpx;
	}

	.evaluate-con .score .textarea {
		width: 100%;
		background-color: #F5F5F5;
		border-radius: 16rpx;
		margin-top: 55rpx;
	}

	.evaluate-con .score .textarea textarea {
		font-size: 28rpx;
		padding: 38rpx 30rpx 0 30rpx;
		width: 100%;
		box-sizing: border-box;
		height: 160rpx;
		width: auto !important;
	}

	.evaluate-con .score .textarea .placeholder {
		color: #bbb;
	}

	.evaluate-con .score .textarea .list {
		margin-top: 25rpx;
		padding-left: 5rpx;
	}

	.evaluate-con .score .textarea .list .pictrue {
		width: 140rpx;
		height: 140rpx;
		margin: 0 0 35rpx 20rpx;
		position: relative;
		font-size: 22rpx;
		border-radius: 16rpx;
	}

	.evaluate-con .score .textarea .list .pictrue:nth-last-child(1) {
		border: 1rpx solid #ddd;
		box-sizing: border-box;
	}

	.evaluate-con .score .textarea .list .pictrue image {
		width: 100%;
		height: 100%;
		border-radius: 16rpx;
	}

	.bottom_close {
		top: -20rpx !important;
		right: -16rpx !important;
		color: #fff !important;
		@include main_bg_color(theme);
	}

	.evaluate-con .score .evaluateBnt {
		font-size: 28rpx;
		color: #fff;
		width: 100%;
		height: 80rpx;
		border-radius: 43rpx;
		text-align: center;
		line-height: 80rpx;
		margin-top: 68rpx;
	}
</style>