<template>
  <PageLayout iconColor="#000" :isBackgroundColor="false" :isShowBack="false" :navTitle="navTitle"
    :bgMaskStyle="bgMaskStyleComputed">
    <!-- 主要内容区域 -->
    <view class="user-content">
      <!-- 用户信息头部 -->
      <view class="user-header">
        <!-- 左侧用户头像 -->
        <view class="avatar-section">
          <image class="avatar tui-skeleton-rect" :src="userInfo.avatar" v-if="userInfo.avatar" @click="goEdit()">
          </image>
          <image v-else class="avatar tui-skeleton-rect" :src="urlDomain + 'crmebimage/presets/morenT.png'" mode=""
            @click="goEdit()">
          </image>
        </view>

        <!-- 中间用户信息 -->
        <view class="user-info" @click="goEdit()">
          <view class="user-name-row">
            <view class="user-name" v-if="!isLogin" @tap="openAuto">
              请点击登录
            </view>
            <view class="user-name" v-if="userInfo && uid">
              {{
                userInfo && userInfo.nickname && uid ? userInfo.nickname : ""
              }}
            </view>
            <view class="user-phone" v-if="userInfo && userInfo.phone && uid">
              {{ userInfo.phone }}
            </view>
          </view>
          <view class="user-level-icon-wrapper">
            <image v-if="userInfo.vipIcon" :src="userInfo.vipIcon" mode="heightFix" class="user-level-icon"
              height="100rpx"></image>
          </view>
        </view>

        <!-- 右侧功能图标 -->
        <view class="action-buttons">
          <!-- <image class="action-icon" :src="actionIcon" mode="aspectFit"></image> -->
        </view>
      </view>

      <!-- 其他用户页面内容 -->
      <!-- TODO: 后续跟换url -->
      <img src="https://tianxing-bucket.oss-cn-hangzhou.aliyuncs.com/4%20%E5%87%A4%E5%87%B0%E9%87%91%E5%8D%A1slogan.png"
        alt="" class="slogan-img" mode="widthFix" />
      <!-- 优惠券部分 -->
      <view class="user-stats-section">
        <view class="stats-item" @click="goToCoupon">
          <view class="stats-number">{{
            userInfo.couponCount && uid ? userInfo.couponCount : 0
          }}</view>
          <view class="stats-label">优惠券</view>
        </view>
        <view class="stats-item" @click="goToCollection">
          <view class="stats-number">{{
            userInfo.collectCount && uid ? userInfo.collectCount : 0
          }}</view>
          <view class="stats-label">我的收藏</view>
        </view>
        <view class="stats-item" @click="goToBrowseHistory">
          <view class="stats-number">{{
            userInfo.browseNum && uid ? userInfo.browseNum : 0
          }}</view>
          <view class="stats-label">浏览足迹</view>
        </view>
        <view class="stats-item" @click="goToPoints">
          <view class="stats-number">{{
            userInfo.integral && uid ? userInfo.integral : 0
          }}</view>
          <view class="stats-label">添星积分</view>
        </view>
      </view>
      <!-- 我的订单 -->
      <view class="my-order-section">
        <!-- 订单标题栏 -->
        <view class="order-header">
          <view class="order-title">我的订单</view>
          <view class="order-all" @click="goToOrderList">
            <text class="all-text">全部</text>
            <image src="/static/images/order/arrow-right.svg" mode="aspectFit" class="arrow-icon" />
          </view>
        </view>

        <!-- 订单状态图标 -->
        <view class="order-status-list">
          <!-- 待付款 -->
          <view class="order-status-item" @click="goToOrderList(0)">
            <view class="status-icon-wrapper">
              <view class="status-icon">
                <image src="/static/images/order/order-wait-pay.svg" mode="aspectFit" class="icon-image" />
              </view>
            </view>
            <view class="status-text">待付款</view>
          </view>

          <!-- 待发货 -->
          <view class="order-status-item" @click="goToOrderList(1)">
            <view class="status-icon-wrapper">
              <view class="status-icon">
                <image src="/static/images/order/order-wait-ship.svg" mode="aspectFit" class="icon-image" />
              </view>
              <!-- 数字徽章 -->
              <view class="badge" v-if="orderCounts.waitShip > 0">
                <text class="badge-text">{{ orderCounts.waitShip }}</text>
              </view>
            </view>
            <view class="status-text">待发货</view>
          </view>

          <!-- 待收货 -->
          <view class="order-status-item" @click="goToOrderList(4)">
            <view class="status-icon-wrapper">
              <view class="status-icon">
                <image src="/static/images/order/order-wait-receive.svg" mode="aspectFit" class="icon-image" />
              </view>
              <!-- 数字徽章 -->
              <view class="badge" v-if="orderCounts.waitReceive > 0">
                <text class="badge-text">{{ orderCounts.waitReceive }}</text>
              </view>
            </view>
            <view class="status-text">待收货</view>
          </view>

          <!-- 待评价 -->
          <view class="order-status-item" @click="goToOrderList(5)">
            <view class="status-icon-wrapper">
              <view class="status-icon">
                <image src="/static/images/order/order-wait-comment.svg" mode="aspectFit" class="icon-image" />
              </view>
            </view>
            <view class="status-text">待评价</view>
          </view>

          <!-- 退换货 -->
          <view class="order-status-item" @click="goToRefundList">
            <view class="status-icon-wrapper">
              <view class="status-icon">
                <image src="/static/images/order/order-refund.svg" mode="aspectFit" class="icon-image" />
              </view>
            </view>
            <view class="status-text">退换货</view>
          </view>
        </view>
      </view>

      <!-- 我的服务 -->
      <view class="my-service-section">
        <view class="service-title">我的服务</view>
        <view class="service-grid">
          <!-- 第一行 -->
          <view class="service-item" @click="goToStarManager">
            <view class="service-icon-wrapper">
              <image src="/static/images/service/service-manager.svg" class="service-icon" />
            </view>
            <view class="service-text">添星管家</view>
          </view>

          <view class="service-item" @click="goToCustomerService">
            <view class="service-icon-wrapper">
              <image src="/static/images/service/service-customer.svg" mode="aspectFit" class="service-icon" />
            </view>
            <view class="service-text">专属客服</view>
          </view>

          <view class="service-item" @click="goToMemberManagement">
            <view class="service-icon-wrapper">
              <image src="/static/images/service/service-member.svg" mode="aspectFit" class="service-icon" />
            </view>
            <view class="service-text">会员管理</view>
          </view>

          <view class="service-item" @click="goToMyTeam">
            <view class="service-icon-wrapper">
              <image src="/static/images/service/service-team.svg" mode="aspectFit" class="service-icon" />
            </view>
            <view class="service-text">我的团队</view>
          </view>

          <!-- 第二行 -->
          <view class="service-item" @click="goToComplaint">
            <view class="service-icon-wrapper">
              <image src="/static/images/service/service-complaint.svg" mode="aspectFit" class="service-icon" />
            </view>
            <view class="service-text">投诉建议</view>
          </view>

          <view class="service-item" @click="goToMyAddress">
            <view class="service-icon-wrapper">
              <image src="/static/images/service/service-address.svg" mode="aspectFit" class="service-icon" />
            </view>
            <view class="service-text">我的地址</view>
          </view>
        </view>
      </view>

      <!-- 精品商品推荐 -->
      <view class="goods-recommendation-section">
        <view class="goods-list-container">
          <!-- 商品卡片 -->
          <view class="goods-card" v-for="goods in goodsList" :key="goods.id" @click="goToGoodsDetail(goods.id)">
            <!-- 商品图片容器 -->
            <view class="goods-image-container">
              <view class="goods-image-mask"></view>
              <image class="goods-image" :src="goods.imageUrl" mode="aspectFill"></image>
            </view>

            <!-- 商品信息 -->
            <view class="goods-info">
              <!-- 商品标题 -->
              <view class="goods-title">{{ goods.title }}</view>

              <!-- 价格行 -->
              <view class="price-row">
                <view class="price-container">
                  <text class="price-symbol">￥</text>
                  <text class="price-amount">{{ goods.price.toFixed(2) }}</text>
                  <text v-if="goods.originalPrice" class="original-price">￥{{ goods.originalPrice.toFixed(2) }}</text>
                </view>
                <view class="sales-info">{{ goods.salesCount }}</view>
              </view>

              <!-- 优惠信息 -->
              <view class="discount-info">{{ goods.discountInfo }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </PageLayout>
</template>
<script>
import PageLayout from "@/components/PageLayout/index.vue";
import Cache from "@/utils/cache";
import { BACK_URL } from "@/config/cache";
import { userCenterInfo } from "@/api/user.js";
import { orderNum } from "@/api/order.js";
import { toLogin } from "@/libs/login.js";
import { mapGetters } from "vuex";
import animationType from "@/utils/animationType.js";

export default {
  data() {
    return {
      navTitle: "",
      urlDomain: this.$Cache.get("imgHost"),

      // 用户信息数据
      userInfo: {
        avatar: "", // 用户头像
        nickname: "", // 用户名
        phone: "", // 电话号码
        vipIcon: "", // VIP图标
        isPaidMember: false, // 是否付费会员
      },
      // 右侧功能图标
      actionIcon: "/static/images/action-icon.png",

      // 订单数量统计
      orderCounts: {
        waitPay: 0, // 待付款
        waitShip: 0, // 待发货
        waitReceive: 0, // 待收货
        waitComment: 0, // 待评价
        refund: 0, // 退换货
      },

      // 精品商品推荐数据
      goodsList: [
        {
          id: 1,
          title: "固本堂黑八宝粉500g*2",
          price: 164.00,
          originalPrice: null,
          salesCount: "已售3000+",
          discountInfo: "债券转让额可抵扣12元",
          imageUrl: "/static/images/936d3e0658d9d29e2c3bdf09bfe388a86f078aa7.png"
        },
        {
          id: 2,
          title: "天然蜂蜜柠檬茶1000ml",
          price: 89.90,
          originalPrice: 129.90,
          salesCount: "已售1500+",
          discountInfo: "限时特惠立减40元",
          imageUrl: "/static/images/936d3e0658d9d29e2c3bdf09bfe388a86f078aa7.png"
        },
        {
          id: 3,
          title: "有机红枣枸杞茶300g",
          price: 68.00,
          originalPrice: null,
          salesCount: "已售800+",
          discountInfo: "买二送一优惠活动",
          imageUrl: "/static/images/936d3e0658d9d29e2c3bdf09bfe388a86f078aa7.png"
        }
      ],
    };
  },

  computed: {
    ...mapGetters(["isLogin", "uid"]),

    /**
     * PageLayout 组件的样式配置
     * 使用 CSS 变量传递背景样式，实现优雅的跨组件样式定制
     */
    bgMaskStyleComputed() {
      return {
        // 定义CSS变量，传递给PageLayout组件的背景遮罩
        "--bg-mask-background": `
         linear-gradient(to right,
            rgba(253, 235, 231, 1) 0%,
            rgba(255, 237, 216, 1) 25%,
            rgba(255, 237, 216, 1) 50%,
            rgba(253, 226, 240, 1) 70%,
            rgba(253, 226, 240, 1) 85%,
            rgba(253, 226, 240, 0.8) 92%,
            rgba(253, 226, 240, 0.4) 97%,
            rgba(253, 226, 240, 0.1) 100%
          ) top left / 100% 220px no-repeat,
          #f2f2f2
        `,
      };
    },
  },
  components: {
    PageLayout,
  },
  onLoad() {
    console.log("页面加载完成");
  },
  onShow() {
    if (this.isLogin) {
      this.getUserCenterInfo();
      this.getOrderCounts();
    }
  },
  methods: {
    /**
     * 跳转到商品详情页
     * @param {number} goodsId 商品ID
     */
    goToGoodsDetail(goodsId) {
      uni.navigateTo({
        animationType: animationType.type,
        animationDuration: animationType.duration,
        url: `/ pages / goods / goods_details / index ? id = ${ goodsId } `,
      });
    },

    /**
     * 获取个人中心详情
     */
    getUserCenterInfo: function () {
      userCenterInfo()
        .then((res) => {
          let data = res.data;
          this.userInfo = data;
          console.log(this.userInfo);
          // TODO: vipIcon 后续跟换url
          this.userInfo.vipIcon =
            "https://tianxing-bucket.oss-cn-hangzhou.aliyuncs.com/4%20%E5%87%A4%E5%87%B0%E9%87%91%E5%8D%A1small.png";
          this.$store.commit("SETUID", data.id);
          this.$store.commit("UPDATE_USERINFO", {
            avatar: data.avatar,
            nickname: data.nickname,
            phone: data.phone,
          });
        })
        .catch((err) => {
          console.error("获取用户信息失败:", err);
        });
    },
    // 编辑页面
    goEdit() {
      if (this.isLogin == false) {
        this.openAuto();
      } else {
        uni.navigateTo({
          animationType: animationType.type,
          animationDuration: animationType.duration,
          url: "/pages/users/user_info/index",
        });
      }
    },
    // 打开授权
    openAuto() {
      Cache.set(BACK_URL, "");
      toLogin();
    },
    // 跳转到优惠券页面
    goToCoupon() {
      if (!this.isLogin) {
        this.openAuto();
        return;
      }
      uni.navigateTo({
        animationType: animationType.type,
        animationDuration: animationType.duration,
        url: "/pages/users/user_coupon/index",
      });
    },
    // 跳转到收藏页面
    goToCollection() {
      if (!this.isLogin) {
        this.openAuto();
        return;
      }
      uni.navigateTo({
        animationType: animationType.type,
        animationDuration: animationType.duration,
        url: "/pages/goods/user_goods_collection/index",
      });
    },
    // 跳转到浏览足迹页面
    goToBrowseHistory() {
      if (!this.isLogin) {
        this.openAuto();
        return;
      }
      uni.navigateTo({
        animationType: animationType.type,
        animationDuration: animationType.duration,
        url: "/pages/goods/browsing_history/index",
      });
    },
    // 跳转到积分页面
    goToPoints() {
      if (!this.isLogin) {
        this.openAuto();
        return;
      }
      uni.navigateTo({
        animationType: animationType.type,
        animationDuration: animationType.duration,
        url: "/pages/merchant/user_integral/index",
      });
    },

    /**
     * 获取订单数量统计
     */
    getOrderCounts() {
      orderNum()
        .then((res) => {
          const data = res.data;
          this.orderCounts = {
            waitPay: data.awaitPayCount || 0,
            waitShip: data.awaitShippedCount || 0,
            waitReceive: data.receiptCount || 0,
            waitComment: data.awaitReplyCount || 0,
            refund: data.refundCount || 0,
          };
        })
        .catch((err) => {
          console.error("获取订单数量失败:", err);
        });
    },

    /**
     * 跳转到订单列表页面
     * @param {number} status 订单状态 0:待付款 1:待发货 4:待收货 5:待评价
     */
    goToOrderList(status) {
      if (!this.isLogin) {
        this.openAuto();
        return;
      }

      let url = "/pages/goods/order_list/index";
      if (status !== undefined) {
        url += `? status = ${ status } `;
      }

      uni.navigateTo({
        animationType: animationType.type,
        animationDuration: animationType.duration,
        url: url,
      });
    },

    /**
     * 跳转到退换货列表页面
     */
    goToRefundList() {
      if (!this.isLogin) {
        this.openAuto();
        return;
      }
      uni.navigateTo({
        animationType: animationType.type,
        animationDuration: animationType.duration,
        url: "/pages/goods/user_return_list/index",
      });
    },

    /**
     * 跳转到添星管家
     */
    goToStarManager() {
      if (!this.isLogin) {
        this.openAuto();
        return;
      }
      // TODO: 实现添星管家功能
      uni.showToast({
        title: "添星管家功能开发中",
        icon: "none",
      });
    },

    /**
     * 跳转到专属客服
     */
    goToCustomerService() {
      if (!this.isLogin) {
        this.openAuto();
        return;
      }
      // TODO: 实现专属客服功能
      uni.showToast({
        title: "专属客服功能开发中",
        icon: "none",
      });
    },

    /**
     * 跳转到会员管理
     */
    goToMemberManagement() {
      if (!this.isLogin) {
        this.openAuto();
        return;
      }
      // TODO: 实现会员管理功能
      uni.showToast({
        title: "会员管理功能开发中",
        icon: "none",
      });
    },

    /**
     * 跳转到我的团队
     */
    goToMyTeam() {
      if (!this.isLogin) {
        this.openAuto();
        return;
      }
      // TODO: 实现我的团队功能
      uni.showToast({
        title: "我的团队功能开发中",
        icon: "none",
      });
    },

    /**
     * 跳转到投诉建议
     */
    goToComplaint() {
      if (!this.isLogin) {
        this.openAuto();
        return;
      }
      // TODO: 实现投诉建议功能
      uni.showToast({
        title: "投诉建议功能开发中",
        icon: "none",
      });
    },

    /**
     * 跳转到我的地址
     */
    goToMyAddress() {
      if (!this.isLogin) {
        this.openAuto();
        return;
      }
      uni.navigateTo({
        animationType: animationType.type,
        animationDuration: animationType.duration,
        url: "/pages/user/address/index",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/* 精品商品推荐样式 */
.goods-recommendation-section {
  width: 100%;
}

.goods-list-container {
  display: flex;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.goods-card {
  position: relative;
  height: 498rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.goods-image-container {
  position: relative;
  width: 100%;
  height: 67.87%;
  overflow: hidden;
  border-radius: 16rpx 16rpx 0 0;
}

.goods-image-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #d8d8d8;
  border-radius: 16rpx 16rpx 0 0;
  z-index: 1;
}

.goods-image {
  position: absolute;
  // top: -10.44rpx; /* -5.22% of 200rpx */
  // left: -15rpx; /* -4.27% of 352rpx */
  width: 352rpx;
  height: 338rpx;
  z-index: 2;
}

.goods-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 32.13%;
  /* 160rpx / 498rpx */
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 16rpx;
}

.goods-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 28rpx;
  line-height: 1;
  color: #222222;
}

.price-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.price-container {
  display: flex;
  align-items: flex-end;
}

.price-symbol {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 22rpx;
  color: #ff0000;
  margin-right: 2rpx;
}

.price-amount {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 32rpx;
  color: #ff0000;
}

.original-price {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 20rpx;
  color: #9d9d9d;
  text-decoration: line-through;
  margin-left: 8rpx;
  display: none;
}

.sales-info {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 24rpx;
  color: #9d9d9d;
}

.discount-info {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 24rpx;
  color: #d97716;
}


.user-content {
  padding: 24rpx;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  overflow-y: auto;
  // background-color: #f2f2f2;
}

/* 用户信息头部布局 */
.user-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 20rpx 0;
  box-sizing: border-box;

  /* 左侧头像区域 */
  .avatar-section {
    flex-shrink: 0;
    height: 136rpx;
    /* 明确指定高度 */
    display: flex;
    align-items: center;
  }

  .avatar {
    width: 136rpx;
    height: 136rpx;
    border-radius: 50%;
    overflow: hidden;
    display: block;
    /* 确保图片按块级元素显示 */
  }

  /* 中间用户信息区域 */
  .user-info {
    flex: 1;
    height: 136rpx;
    display: flex;
    flex-direction: column;
    gap: 18rpx;
    padding: 22rpx 16rpx;

    .user-name-row {
      display: flex;
      margin-bottom: 4rpx;
      /* 减少间距从8rpx到4rpx */
      height: auto;
      gap: 16rpx;
      align-items: center;
    }

    .user-name {
      color: #131313;
      font-size: 32rpx;
      font-weight: 500;
    }

    .user-phone {
      color: #131313;
      font-size: 28rpx;
      font-weight: 400;
    }

    .user-level-icon {
      width: 100%;
      height: 100%;
    }

    .user-level-icon-wrapper {
      height: 32rpx;
    }
  }

  /* 右侧功能按钮区域 */
  .action-buttons {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 120rpx;
  }
}

// 会员卡片
.slogan-img {
  width: 100%;
}

/* 用户统计数据区域 */
.user-stats-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 24rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  .stats-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    padding: 12rpx 0;
    cursor: pointer;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.95);
      opacity: 0.8;
    }

    .stats-number {
      font-family: "PingFang SC", sans-serif;
      font-size: 32rpx;
      font-weight: 600;
      color: #131313;
      margin-bottom: 8rpx;
    }

    .stats-label {
      font-family: "PingFang SC", sans-serif;
      font-size: 20rpx;
      font-weight: 400;
      color: #131313;
    }
  }
}

/* 我的订单部分样式 */
.my-order-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.order-title {
  font-family: "PingFang SC", sans-serif;
  font-size: 32rpx;
  font-weight: 500;
  color: #131313;
}

.order-all {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    opacity: 0.7;
  }
}

.all-text {
  font-family: "PingFang SC", sans-serif;
  font-size: 24rpx;
  font-weight: 400;
  color: #666666;
  margin-right: 8rpx;
}

.arrow-icon {
  width: 8rpx;
  height: 14rpx;
  transform: rotate(180deg) scaleY(-1);
}

.order-status-list {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.order-status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
    opacity: 0.8;
  }
}

.status-icon-wrapper {
  position: relative;
  margin-bottom: 16rpx;
}

.status-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    width: 40rpx;
    height: 40rpx;
  }
}

.icon-image {
  width: 44rpx;
  height: 44rpx;
}

.badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 28rpx;
  height: 28rpx;
  background-color: #ff0000;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge-text {
  font-family: "PingFang SC", sans-serif;
  font-size: 20rpx;
  font-weight: 500;
  color: #ffffff;
  line-height: 1;
}

.status-text {
  font-family: "PingFang SC", sans-serif;
  font-size: 20rpx;
  font-weight: 400;
  color: #131313;
  text-align: center;
}

/* 我的服务模块样式 */
.my-service-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
}

.service-title {
  font-family: "PingFang SC", sans-serif;
  font-size: 32rpx;
  font-weight: 500;
  color: #131313;
  line-height: normal;
  margin-bottom: 32rpx;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 40rpx 32rpx;
  padding: 0 16rpx;
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  gap: 8rpx;

  &:active {
    transform: scale(0.95);
    opacity: 0.8;
  }
}

.service-icon-wrapper {
  width: 68rpx;
  height: 68rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.service-icon {
  width: 100%;
  height: 100%;
}

.service-text {
  font-family: "PingFang SC", sans-serif;
  font-size: 20rpx;
  font-weight: 400;
  color: #131313;
  line-height: normal;
  text-align: center;
}
</style>
