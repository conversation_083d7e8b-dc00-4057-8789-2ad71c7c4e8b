/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/*引入主题变色的scss文件*/
/* 颜色变量 */
@import './static/css/theme.scss';
@import './static/css/theme_color.scss';  
	
/* 行为相关颜色 */
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* crmeb颜色变量 */
$theme-color:#E93323;
$theme-color-opacity:rgba(233,51,35,.6);
$bg-star: #f62c2c;
$bg-end:#f96e29;
$bg-star1: #F73730;  // 主题渐变色1-开始
$bg-end1:#F86429;   // 主题渐变色1-结束


/* 背景颜色 */
$crmeb-theme-color: #1DB0FC;
$crmeb-bg-color:#fff;
$crmeb-bg-color-grey: #F7F7F7; //背景为灰色
$uni-bg-color:#ffffff;
$uni-bg-color-grey:#f8f8f8;
$uni-bg-color-hover:#f1f1f1;//点击状态颜色
$uni-bg-color-mask:rgba(0, 0, 0, 0.4);//遮罩颜色

/* 边框颜色 */
$uni-border-color:#c8c7cc;

/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
$crmeb-font-color: #333333; //文章标题、主要信息、强调内容、段落信息引导词、文字按钮
$crmeb-font-color-subtitle: #666666; //通知公告、列表非编辑文字（如订单详情页内订单号、灰黑色按钮等）
$crmeb-font-color-assist: #999999; //副标题文字、次要信息、辅助、提示（如首页副标题、已售件数等）
$crmeb-font-color-grey: #bbbbbb; //辅助文字、相对弱性文字
$crmeb-font-color-disable: #cccccc; //禁用状态文字、弱提示性文字（如搜索框、禁用按钮等）
$crmeb-font-color-white: #ffffff; //用于彩色或深色背景上
$crmeb-price-color: #FD502F; 
/* 文字尺寸 */
$uni-font-size-sm:24upx;
$uni-font-size-base:28upx;
$uni-font-size-lg:32upx;

/* 图片尺寸 */
$uni-img-size-sm:40upx;
$uni-img-size-base:52upx;
$uni-img-size-lg:80upx;

/* Border Radius */
$uni-border-radius-sm: 4upx;
$uni-border-radius-base: 6upx;
$uni-border-radius-lg: 12upx;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 10px;
$uni-spacing-row-base: 20upx;
$uni-spacing-row-lg: 30upx;

/* 垂直间距 */
$uni-spacing-col-sm: 8upx;
$uni-spacing-col-base: 16upx;
$uni-spacing-col-lg: 24upx;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2C405A; // 文章标题颜色
$uni-font-size-title:40upx;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle:36upx;
$uni-color-paragraph: #3F536E; // 文章段落颜色
$uni-font-size-paragraph:30upx;


